"use client";

import { useEffect } from "react";
import { usePathname, useSearchParams } from "next/navigation";

export default function ClientBody({
  children,
}: {
  children: React.ReactNode;
}) {
  // Remove any extension-added classes during hydration
  useEffect(() => {
    // This runs only on the client after hydration
    document.body.className = "antialiased";
  }, []);

  // 全局自动埋点：监听页面加载和路由变化
  const pathname = usePathname();
  const searchParams = useSearchParams();
  useEffect(() => {
    // 组装埋点数据
    const params: Record<string, string> = {};
    searchParams.forEach((value, key) => {
      params[key] = value;
    });
    const payload = {
      eventType: "pageview",
      path: pathname,
      queryParams: params,
      referer: document.referrer || undefined,
      sessionId: (typeof document !== 'undefined' ? document.cookie.split(';').find(c => c.trim().startsWith('sessionId='))?.split('=')[1] : undefined),
      timestamp: Date.now(),
    };
    fetch("/api/track", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    });
  }, [pathname, searchParams]);

  // 全局捕获 ChunkLoadError，自动刷新以获取最新 chunk
  useEffect(() => {
    let lastReload = 0;
    let reloadCount = 0;
    const MAX_RELOADS = 3; // 最多重试3次

    const handler = (event: ErrorEvent | PromiseRejectionEvent) => {
      const message = (event as ErrorEvent).message || (event as PromiseRejectionEvent).reason?.message || '';
      const stack = (event as ErrorEvent).error?.stack || (event as PromiseRejectionEvent).reason?.stack || '';

      // 检测多种 chunk 加载错误
      const isChunkError =
        message.includes('ChunkLoadError') ||
        message.includes('Loading chunk') ||
        message.includes('Loading CSS chunk') ||
        stack.includes('__webpack_require__.f.j') ||
        stack.includes('webpack.js');

      if (isChunkError) {
        const now = Date.now();
        console.warn('🔄 ChunkLoadError detected:', message);

        // 防止无限重载
        if (reloadCount >= MAX_RELOADS) {
          console.error('❌ Max reload attempts reached, stopping auto-reload');
          return;
        }

        if (now - lastReload > 5000) { // 5 秒内只允许一次自动刷新
          lastReload = now;
          reloadCount++;

          console.log(`🔄 Auto-reloading page (attempt ${reloadCount}/${MAX_RELOADS})...`);

          // 清除可能的缓存
          if ('caches' in window) {
            caches.keys().then(names => {
              names.forEach(name => {
                if (name.includes('next') || name.includes('webpack')) {
                  caches.delete(name);
                }
              });
            });
          }

          // 使用异步，确保错误上报等逻辑有时间执行
          setTimeout(() => {
            window.location.reload();
          }, 100);
        }
      }
    };

    window.addEventListener('error', handler, true);
    window.addEventListener('unhandledrejection', handler);

    return () => {
      window.removeEventListener('error', handler, true);
      window.removeEventListener('unhandledrejection', handler);
    };
  }, []);

  return <div className="antialiased">{children}</div>;
}
