/** @type {import('next').NextConfig} */
const nextConfig = {
  // 性能优化: 仅在生产模式启用 optimizePackageImports
  experimental: process.env.NODE_ENV === 'production' ? {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-dropdown-menu'],
  } : {},

  // 编译优化
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // 图片配置
  images: {
    unoptimized: true,
    domains: [
      "source.unsplash.com",
      "images.unsplash.com",
      "ext.same-assets.com",
      "ugc.same-assets.com",
    ],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "source.unsplash.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "ext.same-assets.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "ugc.same-assets.com",
        pathname: "/**",
      },
    ],
  },

  // Webpack配置优化
  webpack: (config, { isServer, dev }) => {
    // 开发环境优化
    if (dev) {
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
      };

      // 开发环境下禁用代码分割以减少 ChunkLoadError
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            default: false,
            vendors: false,
            // 将所有代码打包到一个 chunk 中
            bundle: {
              name: 'bundle',
              chunks: 'all',
              enforce: true,
            },
          },
        },
      };
    }

    // 处理@napi-rs/canvas原生模块
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push({
        '@napi-rs/canvas': 'commonjs @napi-rs/canvas',
        'canvas': 'commonjs canvas'
      });
    }

    // 忽略原生模块的webpack处理
    config.module.rules.push({
      test: /\.node$/,
      use: 'ignore-loader'
    });

    return config;
  },
};

module.exports = nextConfig;
